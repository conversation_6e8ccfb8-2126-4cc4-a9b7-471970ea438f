import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import { PointsLeaderboard } from "@/components/profile/PointsLeaderboard";
import { getPointsLeaderboard } from "@/services/profileService";

// Mock the service
vi.mock("@/services/profileService", () => ({
  getPointsLeaderboard: vi.fn(),
}));

const mockGetPointsLeaderboard = vi.mocked(getPointsLeaderboard);

describe("PointsLeaderboard", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该显示加载状态", () => {
    mockGetPointsLeaderboard.mockImplementation(() => new Promise(() => {}));

    render(<PointsLeaderboard />);

    expect(screen.getByText("积分排行榜")).toBeInTheDocument();
    expect(screen.getAllByTestId("skeleton")).toHaveLength(5);
  });

  it("应该显示排行榜数据", async () => {
    const mockLeaderboard = [
      {
        id: 1,
        username: "user1",
        nickname: "用户一",
        points: 1000,
        rank: 1,
        title: "专家用户",
      },
      {
        id: 2,
        username: "user2",
        nickname: "用户二",
        points: 800,
        rank: 2,
        title: "活跃用户",
      },
      {
        id: 3,
        username: "user3",
        nickname: "用户三",
        points: 600,
        rank: 3,
        title: "新手",
      },
    ];

    mockGetPointsLeaderboard.mockResolvedValue({
      success: true,
      data: mockLeaderboard,
    });

    render(<PointsLeaderboard limit={10} />);

    await waitFor(() => {
      expect(screen.getByText("用户一")).toBeInTheDocument();
      expect(screen.getByText("用户二")).toBeInTheDocument();
      expect(screen.getByText("用户三")).toBeInTheDocument();
    });

    // 检查积分显示
    expect(screen.getByText("1,000分")).toBeInTheDocument();
    expect(screen.getByText("800分")).toBeInTheDocument();
    expect(screen.getByText("600分")).toBeInTheDocument();

    // 检查头衔显示
    expect(screen.getByText("专家用户")).toBeInTheDocument();
    expect(screen.getByText("活跃用户")).toBeInTheDocument();
    expect(screen.getByText("新手")).toBeInTheDocument();
  });

  it("应该显示空状态", async () => {
    mockGetPointsLeaderboard.mockResolvedValue({
      success: true,
      data: [],
    });

    render(<PointsLeaderboard />);

    await waitFor(() => {
      expect(screen.getByText("暂无排行榜数据")).toBeInTheDocument();
    });
  });

  it("应该显示错误状态", async () => {
    mockGetPointsLeaderboard.mockResolvedValue({
      success: false,
      message: "获取排行榜失败",
    });

    render(<PointsLeaderboard />);

    await waitFor(() => {
      expect(screen.getByText("获取排行榜失败")).toBeInTheDocument();
    });
  });

  it("应该处理网络异常", async () => {
    mockGetPointsLeaderboard.mockRejectedValue(new Error("网络异常"));

    render(<PointsLeaderboard />);

    await waitFor(() => {
      expect(screen.getByText("网络错误，请稍后重试")).toBeInTheDocument();
    });
  });

  it("应该正确显示排名图标", async () => {
    const mockLeaderboard = [
      { id: 1, username: "user1", nickname: "第一名", points: 1000, rank: 1 },
      { id: 2, username: "user2", nickname: "第二名", points: 800, rank: 2 },
      { id: 3, username: "user3", nickname: "第三名", points: 600, rank: 3 },
      { id: 4, username: "user4", nickname: "第四名", points: 400, rank: 4 },
    ];

    mockGetPointsLeaderboard.mockResolvedValue({
      success: true,
      data: mockLeaderboard,
    });

    render(<PointsLeaderboard />);

    await waitFor(() => {
      // 前三名应该有特殊图标，第四名应该显示 #4
      expect(screen.getByText("#4")).toBeInTheDocument();
    });
  });

  it("应该使用自定义limit参数", async () => {
    mockGetPointsLeaderboard.mockResolvedValue({
      success: true,
      data: [],
    });

    render(<PointsLeaderboard limit={5} />);

    await waitFor(() => {
      expect(mockGetPointsLeaderboard).toHaveBeenCalledWith(5);
    });
  });
});
