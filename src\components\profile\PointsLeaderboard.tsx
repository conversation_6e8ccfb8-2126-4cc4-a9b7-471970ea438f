"use client";

import React, { useState, useEffect } from "react";
import { getPointsLeaderboard } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Trophy, Medal, Award, AlertCircle, Crown } from "lucide-react";

interface PointsLeaderboardProps {
  className?: string;
  limit?: number;
}

interface LeaderboardUser {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  points: number;
  rank: number;
  title?: string;
}

export function PointsLeaderboard({
  className,
  limit = 10,
}: PointsLeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadLeaderboard();
  }, [limit]);

  const loadLeaderboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getPointsLeaderboard(limit);

      if (result.success && result.data) {
        // 确保 result.data 是数组，如果不是则使用空数组
        const leaderboardData = Array.isArray(result.data) ? result.data : [];
        setLeaderboard(leaderboardData);
      } else {
        // 出错时确保设置为空数组
        setLeaderboard([]);
        setError(result.message || "获取积分排行榜失败");
      }
    } catch (err) {
      // 异常时确保设置为空数组
      setLeaderboard([]);
      setError("网络错误，请稍后重试");
      console.error("加载积分排行榜失败:", err);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return (
          <span className="text-sm font-medium text-muted-foreground">
            #{rank}
          </span>
        );
    }
  };

  const getRankBadgeVariant = (rank: number) => {
    switch (rank) {
      case 1:
        return "default";
      case 2:
        return "secondary";
      case 3:
        return "outline";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Trophy className="h-5 w-5 mr-2" />
            积分排行榜
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
              <Skeleton className="h-6 w-12" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Trophy className="h-5 w-5 mr-2" />
            积分排行榜
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Trophy className="h-5 w-5 mr-2" />
          积分排行榜
        </CardTitle>
        <p className="text-sm text-muted-foreground mt-1">
          前 {limit} 名用户积分排名
        </p>
      </CardHeader>
      <CardContent>
        {!Array.isArray(leaderboard) || leaderboard.length === 0 ? (
          <div className="text-center py-8">
            <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">暂无排行榜数据</p>
          </div>
        ) : (
          <div className="space-y-3">
            {leaderboard.map((user) => (
              <div
                key={user.id}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                  user.rank <= 3
                    ? "bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800"
                    : "hover:bg-muted/50"
                }`}
              >
                {/* 排名图标 */}
                <div className="flex-shrink-0 w-8 flex justify-center">
                  {getRankIcon(user.rank)}
                </div>

                {/* 用户头像 */}
                <div className="flex-shrink-0">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.nickname || user.username}
                      className="h-8 w-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
                      {(user.nickname || user.username).charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>

                {/* 用户信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium truncate">
                      {user.nickname || user.username}
                    </p>
                    {user.title && (
                      <Badge variant="outline" className="text-xs">
                        {user.title}
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    @{user.username}
                  </p>
                </div>

                {/* 积分 */}
                <div className="flex-shrink-0">
                  <Badge
                    variant={getRankBadgeVariant(user.rank)}
                    className="font-medium"
                  >
                    {user.points.toLocaleString()}分
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
