"use client";

import React, { useState, useEffect } from "react";
import { PointsHistoryItem, getPointsHistory } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  TrendingUp,
  TrendingDown,
  Calendar,
  AlertCircle,
  RefreshCw,
} from "lucide-react";

interface PointsHistoryProps {
  className?: string;
}

export function PointsHistory({ className }: PointsHistoryProps) {
  const [points, setPoints] = useState<PointsHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    loadPointsHistory(1, true);
  }, []);

  const loadPointsHistory = async (
    page: number,
    isInitial: boolean = false
  ) => {
    try {
      if (isInitial) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      const result = await getPointsHistory(page, pageSize);

      if (result.success && result.data) {
        // 确保 items 是数组，如果不是则使用空数组
        const items = Array.isArray(result.data.items) ? result.data.items : [];

        console.log("积分历史数据:", items);

        if (isInitial) {
          // 初始加载，替换数据
          setPoints(items);
          setCurrentPage(1);
        } else {
          // 加载更多，追加数据
          setPoints((prev) => [...prev, ...items]);
        }

        setTotal(result.data.total || 0);

        // 检查是否还有更多数据
        const totalPages = result.data.total_pages || 1;
        setHasMore(page < totalPages);

        if (!isInitial) {
          setCurrentPage(page);
        }
      } else {
        // 出错时确保设置为空数组
        if (isInitial) {
          setPoints([]);
        }
        setError(result.message || "获取积分历史失败");
      }
    } catch (err) {
      // 异常时确保设置为空数组
      if (isInitial) {
        setPoints([]);
      }
      setError("网络错误，请稍后重试");
      console.error("加载积分历史失败:", err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleRefresh = () => {
    setPoints([]);
    setCurrentPage(1);
    setHasMore(true);
    loadPointsHistory(1, true);
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      loadPointsHistory(currentPage + 1, false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "earn":
      case "reward":
      case "bonus":
      case "register":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "spend":
      case "deduct":
      case "penalty":
      case "help_request":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "transfer":
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case "help_answer":
      case "answer_accepted":
        return "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100";
    }
  };

  const getTransactionTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      earn: "获得",
      reward: "奖励",
      bonus: "奖金",
      register: "注册",
      spend: "消费",
      deduct: "扣除",
      penalty: "惩罚",
      help_request: "求助",
      help_answer: "回答",
      answer_accepted: "采纳",
      transfer: "转账",
    };
    return typeMap[type.toLowerCase()] || type;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            积分历史
          </CardTitle>
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            刷新
          </Button>
        </div>
        {total > 0 && (
          <p className="text-sm text-muted-foreground">共 {total} 条记录</p>
        )}
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        ) : !points || points.length === 0 ? (
          <div className="text-center py-8">
            <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">暂无积分记录</p>
          </div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>积分变化</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(points || []).map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center">
                        {item.points > 0 ? (
                          <TrendingUp className="h-4 w-4 text-green-600 mr-2" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600 mr-2" />
                        )}
                        <span
                          className={`font-medium ${
                            item.points > 0 ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {item.points > 0 ? "+" : ""}
                          {item.points}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={getTransactionTypeColor(
                          item.transaction_type
                        )}
                      >
                        {getTransactionTypeText(item.transaction_type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{item.description}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar
                          className="mr-1"
                          style={{
                            width: "12px",
                            height: "12px",
                            flexShrink: 0,
                          }}
                        />
                        {formatDate(item.created_at)}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 加载更多按钮 */}
            <div className="mt-4 flex flex-col items-center space-y-2">
              {loadingMore && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>加载中...</span>
                </div>
              )}

              {hasMore && !loadingMore && (
                <Button
                  onClick={handleLoadMore}
                  variant="outline"
                  size="sm"
                  className="w-32"
                >
                  加载更多
                </Button>
              )}

              {!hasMore && points.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  已显示全部 {total} 条记录
                </p>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
